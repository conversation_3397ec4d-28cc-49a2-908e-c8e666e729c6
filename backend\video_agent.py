import logging

from dotenv import load_dotenv

from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    RoomInputOptions,
    cli,
)
from livekit.plugins import google
from knowledge_manager import KnowledgeManager

logger = logging.getLogger("AI-video-agent")
logger.setLevel(logging.INFO)

load_dotenv()

# Initialize knowledge manager
knowledge_manager = KnowledgeManager()

INSTRUCTIONS = f"""
You are a technical support AI who can provide visual assistance for CloudDash, a SaaS analytics platform, when users share their screen.

IMPORTANT: Respond in plain text only. Do not use any markdown formatting including bold, italics, bullet points, numbered lists, or other markdown syntax. Your responses will be read aloud by text-to-speech.

When screen sharing is available:
- State what you see briefly
- Identify the specific problem  
- Give clear fix steps
- Ask for confirmation only when needed

Focus on:
- Error messages and warning icons
- Broken widgets or "No Data" displays
- Export errors and dialog boxes
- Configuration panels and buttons

When no screen sharing is detected, let the user know they need to share their screen for visual assistance.

Keep responses short while staying helpful and accurate.

{knowledge_manager.format_knowledge()}
"""

class VideoAssistant(Agent):
    """
    Modern LiveKit Agent using Gemini Live API with built-in vision support.
    Follows the official LiveKit documentation pattern.
    """

    def __init__(self) -> None:
        super().__init__(
            instructions=INSTRUCTIONS,
            llm=google.beta.realtime.RealtimeModel(
                model="gemini-2.0-flash-exp",
                voice="Puck",
                temperature=0.8,
                modalities=["audio", "text"],
            ),
        )


async def entrypoint(ctx: JobContext) -> None:
    """
    Modern entrypoint using AgentSession with Gemini Live API.
    Includes automatic video support for screen sharing.
    """
    await ctx.connect()

    logger.info(f"Connected to room: {ctx.room.name}")

    # Create AgentSession with Gemini Live and video support
    session = AgentSession()

    await session.start(
        agent=VideoAssistant(),
        room=ctx.room,
        room_input_options=RoomInputOptions(
            video_enabled=True,  # Enable automatic video processing
            noise_cancellation=True,
        ),
    )

    logger.info("Video assistant started with Gemini Live API")


if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
