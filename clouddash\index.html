<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudDash - Analytics Platform</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <h1>CloudDash</h1>
        </div>
        <div class="nav-menu">
            <a href="#" class="nav-link active" data-page="dashboard">Dashboard</a>
            <a href="#" class="nav-link" data-page="exports">Data Export</a>
        </div>
        <div class="user-info">
            <span>Welcome, <PERSON> (Analyst)</span>
        </div>
    </nav>

    <main class="main-content">
        <!-- Dashboard Page -->
        <div id="dashboard" class="page active">
            <h2>Analytics Dashboard</h2>
            
            <div class="widgets-grid">
                <div class="widget">
                    <div class="widget-header">
                        <h3>Monthly Revenue</h3>
                        <button class="widget-menu" onclick="showWidgetMenu(this)">⚙️</button>
                    </div>
                    <div class="widget-content">
                        <div class="chart-placeholder">
                            <div class="bar" style="height: 60%"></div>
                            <div class="bar" style="height: 80%"></div>
                            <div class="bar" style="height: 45%"></div>
                            <div class="bar" style="height: 90%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="widget" id="broken-widget">
                    <div class="widget-header">
                        <h3>Performance Metrics</h3>
                        <span class="widget-error-large">❌ ERROR: NO DATA</span>
                        <button class="widget-menu" onclick="showWidgetMenu(this)">⚙️</button>
                    </div>
                    <div class="widget-content error">
                        <div class="error-details">
                            <p class="error-main">Widget failed to load data</p>
                            <p class="error-info">Current refresh rate: <span class="highlight-value">1 hour</span></p>
                            <p class="error-info">Status: Data source timeout</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Widget Configuration Modal -->
            <div id="widget-modal" class="modal" style="display: none;">
                <div class="modal-content">
                    <h3>Widget Configuration</h3>
                    <div class="form-group">
                        <label>Refresh Rate:</label>
                        <select id="widget-refresh-rate">
                            <option value="1min">1 minute (Premium only)</option>
                            <option value="5min">5 minutes</option>
                            <option value="15min">15 minutes</option>
                            <option value="1hour" selected>1 hour</option>
                        </select>
                        <p class="help-text">Minimum 5 minutes for enterprise accounts</p>
                    </div>
                    <div class="modal-buttons">
                        <button class="btn-primary" onclick="updateWidget()">Update Widget</button>
                        <button class="btn-secondary" onclick="closeModal()">Cancel</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Export Page -->
        <div id="exports" class="page">
            <h2>Data Export</h2>
            
            <div class="export-form">
                <div class="form-group">
                    <label>Dataset:</label>
                    <select id="dataset-select">
                        <option>Monthly Reports (8,500 rows)</option>
                        <option selected>User Analytics (25,000 rows)</option>
                        <option>Sales Data (45,000 rows)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Format:</label>
                    <div class="radio-group">
                        <label><input type="radio" name="format" value="csv" checked> CSV (Max 10,000 rows)</label>
                        <label><input type="radio" name="format" value="xlsx"> XLSX (Max 100,000 rows)</label>
                        <label><input type="radio" name="format" value="json"> JSON (Unlimited)</label>
                    </div>
                </div>
                
                
                <button class="btn-primary" onclick="exportData()">Generate Export</button>
            </div>
            
            <div id="export-error" class="error-message-large" style="display: none;">
                <div class="error-header">
                    <h2>❌ EXPORT FAILED</h2>
                    <p class="error-type">Error Type: Size Exceeded</p>
                </div>
                <div class="error-details">
                    <p class="error-main">Dataset contains 25,000 rows</p>
                    <p class="error-info">Selected format: CSV (limit: 10,000 rows)</p>
                    <p class="error-info">Export cannot proceed with current settings</p>
                </div>
            </div>
            
            <div id="export-success" class="success-message-large" style="display: none;">
                <div class="success-header">
                    <h2>✅ EXPORT STARTED</h2>
                    <p class="success-type">Status: Processing</p>
                </div>
                <div class="success-details">
                    <p class="success-main">Export started successfully!</p>
                    <p class="success-info">You will receive a notification when the export is ready to download.</p>
                </div>
            </div>
            
        </div>

    </main>

    <script src="script.js"></script>
</body>
</html>