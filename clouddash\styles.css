* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-brand h1 {
    font-size: 1.8rem;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
    cursor: pointer;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
}

.user-info {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.page {
    display: none;
}

.page.active {
    display: block;
}


/* Buttons */
.btn-primary, .btn-secondary {
    padding: 0.7rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
}

.btn-secondary {
    background-color: #f0f0f0;
    color: #333;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
}

/* Widgets Grid */
.widgets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.widget {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s;
}

.widget:hover {
    transform: translateY(-2px);
}

.widget.error {
    border: 2px solid #dc2626;
}

.widget-header {
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-menu {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.3rem;
    border-radius: 3px;
    transition: background-color 0.3s;
}

.widget-menu:hover {
    background-color: rgba(0,0,0,0.1);
}

.widget-menu:hover {
    background-color: rgba(0,0,0,0.1);
}

.widget-header h3 {
    color: #333;
    font-size: 1.1rem;
}

.widget-error {
    color: #ff6b6b;
    font-weight: 600;
    font-size: 0.9rem;
}

.widget-error-large {
    color: #dc2626;
    font-weight: 700;
    font-size: 1rem;
    background-color: #fef2f2;
    padding: 0.4rem 0.6rem;
    border-radius: 4px;
    border: 1px solid #dc2626;
}

.loading {
    color: #ffa726;
    font-size: 0.9rem;
}

.widget-content {
    padding: 1.5rem;
    min-height: 150px;
}

.widget-content.error {
    background-color: #fff5f5;
    color: #e53e3e;
    border-left: 4px solid #fc8181;
}

.performance-slow {
    border-left: 4px solid #ffa726;
}

/* Chart Placeholder */
.chart-placeholder {
    display: flex;
    align-items: end;
    gap: 10px;
    height: 100px;
    margin-top: 20px;
}

.bar {
    background: linear-gradient(180deg, #4CAF50 0%, #45a049 100%);
    width: 40px;
    border-radius: 3px 3px 0 0;
    transition: all 0.3s;
}

.bar:hover {
    opacity: 0.8;
}

/* Export Form */
.export-form {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    max-width: 600px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group select {
    padding: 0.7rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    width: 100%;
}

.form-group input:not([type="radio"]) {
    padding: 0.7rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    width: 100%;
}

.radio-group {
    display: block;
}

.radio-group label {
    display: block;
    font-weight: normal;
    cursor: pointer;
    margin-bottom: 0.5rem;
}

.radio-group input[type="radio"] {
    margin-right: 0.5rem;
}

/* Error Messages */
.error-message {
    background-color: #fff5f5;
    border: 1px solid #fed7d7;
    border-left: 4px solid #fc8181;
    padding: 1.5rem;
    border-radius: 5px;
    margin-top: 2rem;
}

.error-message h3 {
    color: #e53e3e;
    margin-bottom: 0.5rem;
}

.error-message p {
    color: #742a2a;
}

.error-message-large {
    background-color: #fef2f2;
    border: 2px solid #dc2626;
    border-radius: 5px;
    padding: 1.5rem;
    margin-top: 2rem;
    max-width: 600px;
}

.error-header h2 {
    color: #dc2626;
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.error-type {
    color: #991b1b;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.error-details {
    background-color: white;
    padding: 1rem;
    border-radius: 4px;
    border-left: 3px solid #dc2626;
}

.error-main {
    color: #991b1b;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
}

.error-info {
    color: #7f1d1d;
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
}

.highlight-value {
    background-color: #fde68a;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-weight: 600;
    color: #92400e;
}

/* Success Messages */
.success-message-large {
    background-color: #f0fdf4;
    border: 2px solid #16a34a;
    border-radius: 5px;
    padding: 1.5rem;
    margin-top: 2rem;
    max-width: 600px;
}

.success-header h2 {
    color: #16a34a;
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.success-type {
    color: #15803d;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.success-details {
    background-color: white;
    padding: 1rem;
    border-radius: 4px;
    border-left: 3px solid #16a34a;
}

.success-main {
    color: #15803d;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
}

.success-info {
    color: #166534;
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
}


/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 90%;
}

.modal-content h3 {
    margin-bottom: 1.5rem;
    color: #333;
}

.modal-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    justify-content: flex-end;
}

.help-text {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .widgets-grid {
        grid-template-columns: 1fr;
    }
    
}